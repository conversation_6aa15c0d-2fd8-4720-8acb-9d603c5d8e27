package com.example.castapp.utils

import android.content.Context
import android.text.SpannableString
import android.view.Gravity

/**
 * 遥控端富文本格式解析器
 * 用于解析从接收端传输过来的富文本格式数据
 */
class RemoteTextFormatParser(private val context: Context) {

    /**
     * 富文本格式信息数据类
     */
    data class ParsedTextFormat(
        val textContent: String,
        val spannableString: SpannableString? = null,
        val isBold: Boolean = false,
        val isItalic: Boolean = false,
        val fontSize: Int = 13,
        val fontName: String? = null,
        val fontFamily: String? = null,
        val lineSpacing: Float = 0.0f,
        val textAlignment: Int = Gravity.CENTER,
        val hasRichTextFormat: Boolean = false,
        val isWindowColorEnabled: Boolean = false,
        val windowBackgroundColor: Int = 0xFFFFFFFF.toInt()
    )

    /**
     * 解析文字格式数据
     * @param formatData 从接收端传输过来的格式数据
     * @return 解析后的格式信息
     */
    fun parseTextFormat(formatData: Map<String, Any>): ParsedTextFormat {
        try {
            val textContent = formatData["textContent"] as? String ?: ""
            val richTextData = formatData["richTextData"] as? String
            
            AppLog.d("【遥控端格式解析器】开始解析文字格式: 内容长度=${textContent.length}, 富文本数据=${richTextData != null}")

            // 尝试解析富文本格式
            val spannableString = if (richTextData != null && textContent.isNotEmpty()) {
                try {
                    val textFormatManager = TextFormatManager(context)
                    val spannable = textFormatManager.deserializeSpannableString(textContent, richTextData)
                    AppLog.d("【遥控端格式解析器】富文本格式解析成功: 文本长度=${spannable.length}")
                    spannable
                } catch (e: Exception) {
                    AppLog.e("【遥控端格式解析器】富文本格式解析失败", e)
                    null
                }
            } else {
                null
            }

            // 解析基本格式属性（支持类型转换）
            val isBold = formatData["isBold"] as? Boolean ?: false
            val isItalic = formatData["isItalic"] as? Boolean ?: false
            val fontSize = when (val size = formatData["fontSize"]) {
                is Int -> size
                is Float -> size.toInt()
                is Double -> size.toInt()
                is Number -> size.toInt()
                else -> 13
            }
            val fontName = formatData["fontName"] as? String
            val fontFamily = formatData["fontFamily"] as? String
            val lineSpacing = when (val spacing = formatData["lineSpacing"]) {
                is Float -> spacing
                is Double -> spacing.toFloat()
                is Number -> spacing.toFloat()
                else -> 0.0f
            }
            AppLog.d("【遥控端格式解析器】行间距解析调试: 原始值=${formatData["lineSpacing"]}, 类型=${formatData["lineSpacing"]?.javaClass?.simpleName}, 解析后=${lineSpacing}dp")
            val textAlignment = when (val alignment = formatData["textAlignment"]) {
                is Int -> alignment
                is Float -> alignment.toInt()
                is Double -> alignment.toInt()
                is Number -> alignment.toInt()
                else -> Gravity.CENTER
            }
            AppLog.d("【遥控端格式解析器】对齐解析调试: 原始值=${formatData["textAlignment"]}, 类型=${formatData["textAlignment"]?.javaClass?.simpleName}, 解析后=${textAlignment}")
            val isWindowColorEnabled = formatData["isWindowColorEnabled"] as? Boolean ?: false
            val windowBackgroundColor = when (val color = formatData["windowBackgroundColor"]) {
                is Int -> color
                is Long -> color.toInt()
                is Float -> color.toInt()
                is Double -> color.toInt()
                is Number -> color.toInt()
                else -> 0xFFFFFFFF.toInt()
            }

            AppLog.d("【遥控端格式解析器】颜色解析调试: 原始值=${formatData["windowBackgroundColor"]}, 类型=${formatData["windowBackgroundColor"]?.javaClass?.simpleName}, 解析后=${String.format("#%08X", windowBackgroundColor)}")

            val parsedFormat = ParsedTextFormat(
                textContent = textContent,
                spannableString = spannableString,
                isBold = isBold,
                isItalic = isItalic,
                fontSize = fontSize,
                fontName = fontName,
                fontFamily = fontFamily,
                lineSpacing = lineSpacing,
                textAlignment = textAlignment,
                hasRichTextFormat = spannableString != null,
                isWindowColorEnabled = isWindowColorEnabled,
                windowBackgroundColor = windowBackgroundColor
            )

            AppLog.d("【遥控端格式解析器】格式解析完成: 富文本=${parsedFormat.hasRichTextFormat}, 字号=${fontSize}sp, 行间距=${lineSpacing}dp")
            AppLog.d("【遥控端格式解析器】详细格式信息: 粗体=${isBold}, 斜体=${isItalic}, 对齐=${textAlignment}, SpannableString=${spannableString != null}")
            AppLog.d("【遥控端格式解析器】窗口背景颜色: 启用=${isWindowColorEnabled}, 颜色=${String.format("#%08X", windowBackgroundColor)}")

            if (spannableString != null) {
                AppLog.d("【遥控端格式解析器】SpannableString详情: 长度=${spannableString.length}, 内容='${spannableString}'")
                // 输出Span信息用于调试
                val spans = spannableString.getSpans(0, spannableString.length, Any::class.java)
                AppLog.d("【遥控端格式解析器】Span数量: ${spans.size}")
                spans.forEachIndexed { index, span ->
                    val start = spannableString.getSpanStart(span)
                    val end = spannableString.getSpanEnd(span)
                    AppLog.d("【遥控端格式解析器】Span[$index]: ${span::class.simpleName} 范围[$start-$end]")
                }
            }

            return parsedFormat

        } catch (e: Exception) {
            AppLog.e("【遥控端格式解析器】解析文字格式失败", e)
            
            // 返回基本格式作为后备方案
            val textContent = formatData["textContent"] as? String ?: ""
            return ParsedTextFormat(
                textContent = textContent,
                hasRichTextFormat = false
            )
        }
    }

    /**
     * 批量解析文字格式数据
     * @param textContentsData 文字内容数据列表
     * @return 解析后的格式信息映射
     */
    fun parseTextFormats(textContentsData: List<Map<String, Any>>): Map<String, ParsedTextFormat> {
        val parsedFormats = mutableMapOf<String, ParsedTextFormat>()
        
        textContentsData.forEach { formatData ->
            val connectionId = formatData["connectionId"] as? String
            if (connectionId != null) {
                val parsedFormat = parseTextFormat(formatData)
                parsedFormats[connectionId] = parsedFormat
                AppLog.d("【遥控端格式解析器】已解析文字格式: $connectionId")
            }
        }
        
        AppLog.d("【遥控端格式解析器】批量解析完成: ${parsedFormats.size} 个文字窗口")
        return parsedFormats
    }
}
