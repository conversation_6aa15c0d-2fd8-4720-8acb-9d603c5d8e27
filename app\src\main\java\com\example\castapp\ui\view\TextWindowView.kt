package com.example.castapp.ui.view

import android.content.Context
import android.graphics.Typeface
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import com.example.castapp.utils.StrokeSpan
import com.example.castapp.utils.LetterSpacingSpan
import android.text.TextWatcher
import android.text.Editable
import android.util.AttributeSet
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.inputmethod.InputMethodManager
import android.widget.FrameLayout
import androidx.appcompat.widget.AppCompatEditText
import com.example.castapp.utils.AppLog

/**
 * 文本窗口显示组件
 * 支持格式化文本显示和直接编辑功能
 */
class TextWindowView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AppCompatEditText(context, attrs, defStyleAttr) {

    // 双击检测器
    private val gestureDetector: GestureDetector

    // 双击监听器
    private var onDoubleClickListener: (() -> Unit)? = null

    // 编辑状态监听器
    private var onEditStateChangeListener: ((Boolean) -> Unit)? = null
    private var onTextChangeListener: ((String) -> Unit)? = null
    private var onSizeChangeListener: ((Int, Int) -> Unit)? = null
    private var onSelectionChangeListener: ((Boolean, Boolean) -> Unit)? = null
    private var onSelectionChangeWithColorListener: ((Boolean, Boolean, Int?) -> Unit)? = null
    private var onSelectionChangeWithAllFormatsListener: ((Boolean, Boolean, Int?, Triple<Boolean, Float, Int>?) -> Unit)? = null

    // 文本格式状态
    private var isBoldEnabled = false
    private var isItalicEnabled = false
    private var currentFontSizeSp = 13 // 直接存储sp值，避免像素转换
    private var currentFontFamily: com.example.castapp.utils.FontPresetManager.FontItem? = null // 当前字体

    // 原始文本内容（不包含格式）
    private var rawTextContent = "默认文字"

    // 编辑模式状态
    private var isInEditMode = false

    // 文本编辑边框视图
    private var textEditBorderView: TextEditBorderView? = null

    // 窗口背景颜色状态
    private var isWindowColorEnabled = false
    private var windowBackgroundColor = 0xFFFFFFFF.toInt()

    init {
        // 初始化双击检测器
        gestureDetector = GestureDetector(context, object : GestureDetector.SimpleOnGestureListener() {
            override fun onDoubleTap(e: MotionEvent): Boolean {
                AppLog.d("【文本窗口】检测到双击事件")
                if (!isInEditMode) {
                    enterEditMode()
                }
                onDoubleClickListener?.invoke()
                return true
            }

            override fun onSingleTapConfirmed(e: MotionEvent): Boolean {
                AppLog.v("【文本窗口】检测到单击事件")
                if (isInEditMode) {
                    // 编辑模式下，单击用于定位光标
                    return true
                }
                return false // 不消费单击事件，让父容器处理
            }

            override fun onDown(e: MotionEvent): Boolean {
                AppLog.v("【文本窗口】检测到按下事件")
                return true // 表示我们对这个手势感兴趣
            }
        })

        // 设置默认样式
        setupDefaultStyle()

        // 设置默认文本
        updateFormattedText()

        // 设置文本变化监听器
        setupTextWatcher()

        // 🎯 关键设置：确保视图可以接收触摸事件
        isClickable = true
        isFocusable = true
        isFocusableInTouchMode = true

        AppLog.d("【文本窗口】TextWindowView 初始化完成")
    }
    
    /**
     * 设置默认样式
     */
    private fun setupDefaultStyle() {
        // 使用新的字号设置方法
        setFontSizeSp(13)
        setTextColor(0xFF333333.toInt())
        setPadding(16, 16, 16, 16)
        gravity = android.view.Gravity.CENTER_HORIZONTAL or android.view.Gravity.CENTER_VERTICAL
        background = null // 透明背景，由容器处理边框和圆角

        // 默认不可编辑，双击后进入编辑模式
        isEnabled = false
        isCursorVisible = false

        // 设置默认字体为"Roboto"
        setupDefaultFont()

        AppLog.d("【文本窗口】默认样式设置完成，字号: ${currentFontSizeSp}sp")

        // 设置文本换行属性
        isSingleLine = false
        maxLines = Integer.MAX_VALUE

        AppLog.d("【文本窗口】默认样式设置完成，字号: 13sp")
    }

    /**
     * 设置默认字体
     */
    private fun setupDefaultFont() {
        try {
            // 初始化FontPresetManager
            com.example.castapp.utils.FontPresetManager.initialize(context)

            // 获取默认的"Roboto"字体
            val allFonts = com.example.castapp.utils.FontPresetManager.getAllFonts()
            val defaultFont = allFonts.find { it.name == "Roboto" }

            if (defaultFont != null) {
                currentFontFamily = defaultFont
                setFontFamily(defaultFont)
                AppLog.d("【文本窗口】默认字体已设置: ${defaultFont.name}")
            } else {
                AppLog.w("【文本窗口】未找到Roboto字体，使用系统默认字体")
                this.typeface = android.graphics.Typeface.DEFAULT
            }

        } catch (e: Exception) {
            AppLog.e("【文本窗口】设置默认字体失败", e)
            this.typeface = android.graphics.Typeface.DEFAULT
        }
    }

    /**
     * 设置文本变化监听器
     */
    private fun setupTextWatcher() {
        addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

            override fun afterTextChanged(s: Editable?) {
                if (isInEditMode && s != null) {
                    rawTextContent = s.toString()
                    onTextChangeListener?.invoke(rawTextContent)
                    AppLog.d("【文本窗口】文本内容已更新: $rawTextContent")
                }
            }
        })
    }


    /**
     * 设置编辑状态变化监听器
     */
    fun setOnEditStateChangeListener(listener: (Boolean) -> Unit) {
        this.onEditStateChangeListener = listener
        AppLog.d("【文本窗口】编辑状态监听器已设置")
    }

    /**
     * 设置文本变化监听器
     */
    fun setOnTextChangeListener(listener: (String) -> Unit) {
        this.onTextChangeListener = listener
        AppLog.d("【文本窗口】文本变化监听器已设置")
    }

    /**
     * 设置尺寸变化监听器
     */
    fun setOnSizeChangeListener(listener: (Int, Int) -> Unit) {
        this.onSizeChangeListener = listener
        AppLog.d("【文本窗口】尺寸变化监听器已设置")
    }

    /**
     * 设置选择变化监听器（包含所有格式信息）
     */
    fun setOnSelectionChangeWithAllFormatsListener(listener: (Boolean, Boolean, Int?, Triple<Boolean, Float, Int>?) -> Unit) {
        this.onSelectionChangeWithAllFormatsListener = listener
        AppLog.d("【文本窗口】选择变化监听器（含所有格式）已设置")
    }

    /**
     * 进入编辑模式
     */
    fun enterEditMode() {
        if (isInEditMode) return

        isInEditMode = true
        isEnabled = true
        isCursorVisible = true
        isFocusableInTouchMode = true

        // 显示文本编辑边框
        showTextEditBorder()

        // 🔧 修复：不要重新格式化文本，保持现有的所有格式
        // updateFormattedText() // 移除这行，避免重置用户设置的格式

        // 请求焦点并显示键盘
        requestFocus()

        // 🎯 修复：将光标定位到文本结尾，而不是自动全选
        setSelection(text?.length ?: 0)

        // 显示软键盘
        val imm = context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.showSoftInput(this, InputMethodManager.SHOW_IMPLICIT)

        onEditStateChangeListener?.invoke(true)
        AppLog.d("【文本窗口】进入编辑模式，光标定位到文本结尾")
    }

    /**
     * 退出编辑模式
     */
    fun exitEditMode() {
        if (!isInEditMode) return

        isInEditMode = false
        isEnabled = false
        isCursorVisible = false

        // 隐藏文本编辑边框
        hideTextEditBorder()

        // 清除焦点
        clearFocus()

        // 隐藏软键盘
        val imm = context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.hideSoftInputFromWindow(windowToken, 0)

        // 🔧 修复：不要重新格式化文本，保持编辑模式中应用的所有格式
        // updateFormattedText() // 移除这行，避免重置用户设置的格式

        onEditStateChangeListener?.invoke(false)
        AppLog.d("【文本窗口】退出编辑模式，保持当前文本格式")
    }

    /**
     * 显示文本编辑边框
     */
    private fun showTextEditBorder() {
        val parentContainer = parent as? FrameLayout ?: return

        // 创建文本编辑边框视图
        textEditBorderView = TextEditBorderView(context).apply {
            // 设置边框尺寸与文本窗口相同
            layoutParams = FrameLayout.LayoutParams(
                <EMAIL>,
                <EMAIL>
            )

            // 设置尺寸变化监听器
            setOnSizeChangeListener { newWidth, newHeight ->
                // 同步调整文本窗口的尺寸
                updateTextWindowSize(newWidth, newHeight)
            }
        }

        // 添加到父容器，与文本窗口重叠
        parentContainer.addView(textEditBorderView)

        // 设置边框位置与文本窗口重叠
        textEditBorderView?.x = this.x
        textEditBorderView?.y = this.y
        textEditBorderView?.bringToFront()

        AppLog.d("【文本窗口】文本编辑边框已显示")
    }

    /**
     * 隐藏文本编辑边框
     */
    private fun hideTextEditBorder() {
        textEditBorderView?.let { borderView ->
            val parentContainer = parent as? FrameLayout
            parentContainer?.removeView(borderView)
            textEditBorderView = null
            AppLog.d("【文本窗口】文本编辑边框已隐藏")
        }
    }

    /**
     * 更新文本窗口尺寸（私有方法，用于编辑模式下的拖动调整）
     */
    private fun updateTextWindowSize(newWidth: Int, newHeight: Int) {
        try {
            // 更新文本窗口的布局参数
            val layoutParams = this.layoutParams
            layoutParams.width = newWidth
            layoutParams.height = newHeight
            this.layoutParams = layoutParams

            // 同步更新边框位置
            textEditBorderView?.x = this.x
            textEditBorderView?.y = this.y

            // 通知外部尺寸变化
            onSizeChangeListener?.invoke(newWidth, newHeight)

            AppLog.d("【文本窗口】尺寸已同步更新: ${newWidth}x${newHeight}")

        } catch (e: Exception) {
            AppLog.e("【文本窗口】更新尺寸失败", e)
        }
    }

    /**
     * 设置文本窗口尺寸（公共方法，用于外部设置尺寸）
     */
    fun setWindowSize(newWidth: Int, newHeight: Int) {
        try {
            // 更新文本窗口的布局参数
            val layoutParams = this.layoutParams
            layoutParams.width = newWidth
            layoutParams.height = newHeight
            this.layoutParams = layoutParams

            // 如果在编辑模式下，同步更新边框尺寸和位置
            textEditBorderView?.let { borderView ->
                val borderLayoutParams = borderView.layoutParams
                borderLayoutParams.width = newWidth
                borderLayoutParams.height = newHeight
                borderView.layoutParams = borderLayoutParams
                borderView.x = this.x
                borderView.y = this.y
            }

            // 通知外部尺寸变化
            onSizeChangeListener?.invoke(newWidth, newHeight)

            AppLog.d("【文本窗口】外部尺寸设置完成: ${newWidth}x${newHeight}")

        } catch (e: Exception) {
            AppLog.e("【文本窗口】设置尺寸失败", e)
        }
    }


    
    /**
     * 设置文本内容
     */
    fun setTextContent(content: String) {
        if (content.isNotBlank()) {
            rawTextContent = content
            // 只在编辑模式下或初始化时使用格式化文本，避免覆盖用户设置的格式
            if (isInEditMode || text.isNullOrEmpty()) {
                updateFormattedText()
                AppLog.d("【文本窗口】文本内容已更新并格式化: $content")
            } else {
                // 非编辑模式下，只更新原始内容，保持现有格式
                AppLog.d("【文本窗口】文本内容已更新，保持现有格式: $content")
            }
        }
    }

    /**
     * 设置加粗状态（全局格式，向后兼容）
     */
    fun setBoldEnabled(enabled: Boolean) {
        if (isBoldEnabled != enabled) {
            isBoldEnabled = enabled
            updateFormattedText()
            AppLog.d("【文本窗口】全局加粗状态已更新: $enabled")
        }
    }

    /**
     * 设置倾斜状态（全局格式，向后兼容）
     */
    fun setItalicEnabled(enabled: Boolean) {
        if (isItalicEnabled != enabled) {
            isItalicEnabled = enabled
            updateFormattedText()
            AppLog.d("【文本窗口】全局倾斜状态已更新: $enabled")
        }
    }

    /**
     * 设置文本对齐方式
     */
    fun setTextGravity(alignment: Int) {
        if (gravity != alignment) {
            gravity = alignment
            // 强制刷新TextView以确保对齐生效
            post {
                requestLayout()
                invalidate()
            }
            AppLog.d("【文本窗口】文本对齐方式已更新: $alignment")
        }
    }

    /**
     * 获取当前文本对齐方式
     */
    fun getTextGravity(): Int {
        return gravity
    }

    /**
     * 对选中文字应用对齐方式
     */
    fun applyTextAlignmentToSelection(alignment: Int) {
        if (!isInEditMode) {
            AppLog.w("【文本窗口】非编辑模式下无法应用选择对齐")
            return
        }

        val start = selectionStart
        val end = selectionEnd

        if (start == end) {
            // 没有选中文字，应用到全局对齐方式
            setTextGravity(alignment)
            AppLog.d("【文本窗口】无选中文字，应用全局对齐方式: $alignment")
        } else {
            // 有选中文字时，对齐方式应用到整个TextView（因为对齐是TextView级别的属性）
            setTextGravity(alignment)
            AppLog.d("【文本窗口】对选中文字应用对齐方式: $alignment, 范围: $start-$end")
        }
    }

    /**
     * 设置字号
     */
    fun setFontSize(size: Int) {
        textSize = size.toFloat()
        AppLog.d("【文本窗口】字号已更新: ${size}sp")
    }

    /**
     * 设置字号（sp值）
     */
    fun setFontSizeSp(fontSizeSp: Int) {
        currentFontSizeSp = fontSizeSp
        textSize = fontSizeSp.toFloat() // TextView.setTextSize默认使用sp单位
        AppLog.d("【文本窗口】字号已设置: ${fontSizeSp}sp")
    }

    /**
     * 获取当前字号
     */
    fun getCurrentFontSize(): Int {
        return currentFontSizeSp
    }

    /**
     * 设置字体族
     */
    fun setFontFamily(fontItem: com.example.castapp.utils.FontPresetManager.FontItem?) {
        try {
            currentFontFamily = fontItem
            val typeface = fontItem?.loadTypeface()

            if (typeface != null) {
                this.typeface = typeface
                AppLog.d("【文本窗口】字体已更新: ${fontItem.name}")
            } else {
                // 如果字体加载失败，使用默认字体
                this.typeface = android.graphics.Typeface.DEFAULT
                AppLog.w("【文本窗口】字体加载失败，使用默认字体: ${fontItem?.name}")
            }

        } catch (e: Exception) {
            AppLog.e("【文本窗口】设置字体失败", e)
            this.typeface = android.graphics.Typeface.DEFAULT
        }
    }

    /**
     * 对选中文字应用字体
     */
    fun applyFontFamilyToSelection(fontItem: com.example.castapp.utils.FontPresetManager.FontItem?) {
        if (!isInEditMode) {
            AppLog.w("【文本窗口】非编辑模式下无法应用选择字体")
            return
        }

        val start = selectionStart
        val end = selectionEnd

        if (start == end) {
            // 没有选中文字，应用到全局字体（新输入的文字会使用这个字体）
            setFontFamily(fontItem)
            AppLog.d("【文本窗口】无选中文字，更新全局字体: ${fontItem?.name}")
        } else {
            // 有选中文字，只对选中部分应用字体
            applyFontFamilyToRange(start, end, fontItem)
            AppLog.d("【文本窗口】对选中文字应用字体: ${fontItem?.name}, 范围: $start-$end")
        }
    }

    /**
     * 对选中文字应用字号
     */
    fun applyFontSizeToSelection(fontSize: Int) {
        if (!isInEditMode) {
            AppLog.w("【文本窗口】非编辑模式下无法应用选择字号")
            return
        }

        val start = selectionStart
        val end = selectionEnd

        if (start == end) {
            // 没有选中文字，应用到全局字号（新输入的文字会使用这个字号）
            currentFontSizeSp = fontSize
            AppLog.d("【文本窗口】无选中文字，更新全局字号: ${fontSize}sp")
        } else {
            // 有选中文字，只对选中部分应用字号
            applyFontSizeToRange(start, end, fontSize)
            AppLog.d("【文本窗口】对选中文字应用字号: ${fontSize}sp, 范围: $start-$end")
        }
    }

    /**
     * 对选中文字应用字间距
     */
    fun applyLetterSpacingToSelection(letterSpacing: Float) {
        if (!isInEditMode) {
            AppLog.w("【文本窗口】非编辑模式下无法应用选择字间距")
            return
        }

        val start = selectionStart
        val end = selectionEnd

        if (start == end) {
            // 没有选中文字，应用到全局字间距（新输入的文字会使用这个字间距）
            // 注意：由于字间距是通过Span实现的，全局设置需要特殊处理
            AppLog.d("【文本窗口】无选中文字，字间距将应用到新输入的文字: ${letterSpacing}em")
        } else {
            // 有选中文字，只对选中部分应用字间距
            applyLetterSpacingToRange(start, end, letterSpacing)
            AppLog.d("【文本窗口】对选中文字应用字间距: ${letterSpacing}em, 范围: $start-$end")
        }
    }

    /**
     * 对选中文字应用行间距
     * 🎯 修复：行间距是TextView级别的属性，始终应用到整个TextView
     */
    fun applyLineSpacingToSelection(lineSpacing: Float) {
        if (!isInEditMode) {
            AppLog.w("【文本窗口】非编辑模式下无法应用选择行间距")
            return
        }

        // 🎯 关键修复：行间距始终应用到整个TextView，无论是否有选中文字
        applyGlobalLineSpacing(lineSpacing)
        AppLog.d("【文本窗口】应用全局行间距: ${lineSpacing}dp（行间距是TextView级别的属性）")
    }

    /**
     * 应用全局行间距
     */
    private fun applyGlobalLineSpacing(lineSpacing: Float) {
        try {
            // 使用TextView的内置行间距功能
            val lineSpacingMultiplier = 1.0f
            val lineSpacingExtra = lineSpacing * resources.displayMetrics.density

            setLineSpacing(lineSpacingExtra, lineSpacingMultiplier)

            // 强制刷新布局
            post {
                requestLayout()
                invalidate()
            }

            AppLog.d("【文本窗口】已应用全局行间距: ${lineSpacing}dp (${lineSpacingExtra}px)")

        } catch (e: Exception) {
            AppLog.e("【文本窗口】应用全局行间距失败", e)
        }
    }

    /**
     * 对选中文字应用加粗格式
     */
    fun applyBoldToSelection(enabled: Boolean) {
        if (!isInEditMode) {
            AppLog.w("【文本窗口】非编辑模式下无法应用选择格式")
            return
        }

        val start = selectionStart
        val end = selectionEnd

        if (start == end) {
            // 没有选中文字，应用到全局格式（新输入的文字会使用这个格式）
            isBoldEnabled = enabled
            AppLog.d("【文本窗口】无选中文字，更新全局加粗状态: $enabled")
        } else {
            // 有选中文字，只对选中部分应用格式
            applyStyleToRange(start, end, Typeface.BOLD, enabled)
            AppLog.d("【文本窗口】对选中文字应用加粗: $enabled, 范围: $start-$end")
        }
    }

    /**
     * 对选中文字应用倾斜格式
     */
    fun applyItalicToSelection(enabled: Boolean) {
        if (!isInEditMode) {
            AppLog.w("【文本窗口】非编辑模式下无法应用选择格式")
            return
        }

        val start = selectionStart
        val end = selectionEnd

        if (start == end) {
            // 没有选中文字，应用到全局格式
            isItalicEnabled = enabled
            AppLog.d("【文本窗口】无选中文字，更新全局倾斜状态: $enabled")
        } else {
            // 有选中文字，只对选中部分应用格式
            applyStyleToRange(start, end, Typeface.ITALIC, enabled)
            AppLog.d("【文本窗口】对选中文字应用倾斜: $enabled, 范围: $start-$end")
        }
    }

    /**
     * 对选中文字应用颜色
     */
    fun applyColorToSelection(color: Int) {
        if (!isInEditMode) {
            AppLog.w("【文本窗口】非编辑模式下无法应用选择颜色")
            return
        }

        val start = selectionStart
        val end = selectionEnd

        if (start == end) {
            // 没有选中文字，暂时不支持全局颜色设置
            AppLog.d("【文本窗口】无选中文字，暂不支持全局颜色设置")
        } else {
            // 有选中文字，只对选中部分应用颜色
            applyColorToRange(start, end, color)
            AppLog.d("【文本窗口】对选中文字应用颜色: ${String.format("#%08X", color)}, 范围: $start-$end")
        }
    }

    /**
     * 对选中文字应用描边
     */
    fun applyStrokeToSelection(enabled: Boolean, strokeWidth: Float, strokeColor: Int) {
        if (!isInEditMode) {
            AppLog.w("【文本窗口】非编辑模式下无法应用描边")
            return
        }

        val start = selectionStart
        val end = selectionEnd

        if (start == end) {
            // 没有选中文字，暂时不支持全局描边设置
            AppLog.d("【文本窗口】无选中文字，暂不支持全局描边设置")
        } else {
            // 有选中文字，应用或移除描边
            if (enabled) {
                applyStrokeToRange(start, end, strokeWidth, strokeColor)
                AppLog.d("【文本窗口】对选中文字应用描边: 宽度=${strokeWidth}px, 颜色=${String.format("#%08X", strokeColor)}, 范围: $start-$end")
            } else {
                removeStrokeFromRange(start, end)
                AppLog.d("【文本窗口】移除选中文字的描边, 范围: $start-$end")
            }
        }
    }

    /**
     * 清除选中文字的所有格式
     */
    fun clearSelectionFormat() {
        if (!isInEditMode) {
            AppLog.w("【文本窗口】非编辑模式下无法清除选择格式")
            return
        }

        val start = selectionStart
        val end = selectionEnd

        if (start == end) {
            // 没有选中文字，清除全局格式
            isBoldEnabled = false
            isItalicEnabled = false
            AppLog.d("【文本窗口】无选中文字，清除全局格式")
        } else {
            // 有选中文字，清除选中部分的格式
            clearStyleFromRange(start, end)
            AppLog.d("【文本窗口】清除选中文字格式，范围: $start-$end")
        }
    }

    /**
     * 📝 应用格式到整个文本（用于布局恢复）
     */
    fun applyFormatToAllText(bold: Boolean, italic: Boolean, fontSize: Int) {
        try {
            // 更新全局格式状态
            isBoldEnabled = bold
            isItalicEnabled = italic
            currentFontSizeSp = fontSize

            // 如果在编辑模式下，需要应用到整个文本
            if (isInEditMode) {
                val textLength = text?.length ?: 0
                if (textLength > 0) {
                    // 应用格式到整个文本
                    applyStyleToRange(0, textLength, Typeface.BOLD, bold)
                    applyStyleToRange(0, textLength, Typeface.ITALIC, italic)
                    applyFontSizeToRange(0, textLength, fontSize)
                }
            } else {
                // 非编辑模式下，更新格式化文本
                updateFormattedText()
            }

            AppLog.d("【文本窗口】格式已应用到整个文本: 加粗=$bold, 倾斜=$italic, 字号=${fontSize}sp")

        } catch (e: Exception) {
            AppLog.e("【文本窗口】应用格式到整个文本失败", e)
        }
    }
    
    /**
     * 更新格式化文本显示
     */
    private fun updateFormattedText() {
        val spannableString = SpannableString(rawTextContent)

        // 应用格式化样式（编辑模式和非编辑模式都应用）
        if (isBoldEnabled || isItalicEnabled) {
            var style = 0
            if (isBoldEnabled) style = style or Typeface.BOLD
            if (isItalicEnabled) style = style or Typeface.ITALIC

            val styleSpan = StyleSpan(style)
            spannableString.setSpan(styleSpan, 0, rawTextContent.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        }

        // 在编辑模式下需要保持光标位置和选择状态
        if (isInEditMode) {
            val currentSelection = selectionStart
            val currentSelectionEnd = selectionEnd
            setText(spannableString)
            // 恢复光标位置
            val textLength = text?.length ?: 0
            if (currentSelection >= 0 && currentSelection <= textLength) {
                if (currentSelectionEnd >= 0 && currentSelectionEnd <= textLength && currentSelectionEnd != currentSelection) {
                    setSelection(currentSelection, currentSelectionEnd)
                } else {
                    setSelection(currentSelection)
                }
            }
            AppLog.v("【文本窗口】编辑模式格式化文本已更新: 加粗=$isBoldEnabled, 倾斜=$isItalicEnabled, 光标位置=$currentSelection")
        } else {
            setText(spannableString)
            AppLog.v("【文本窗口】非编辑模式格式化文本已更新: 加粗=$isBoldEnabled, 倾斜=$isItalicEnabled")
        }
    }
    
    /**
     * 对指定范围应用样式
     */
    private fun applyStyleToRange(start: Int, end: Int, style: Int, enabled: Boolean) {
        val editable = text ?: return

        // 移除该范围内现有的相同类型样式
        val existingSpans = editable.getSpans(start, end, StyleSpan::class.java)
        for (span in existingSpans) {
            val spanStart = editable.getSpanStart(span)
            val spanEnd = editable.getSpanEnd(span)
            val spanStyle = span.style

            // 如果是相同类型的样式，需要处理重叠
            if ((spanStyle and style) != 0) {
                editable.removeSpan(span)

                // 如果原样式范围超出了要修改的范围，需要保留超出部分
                if (spanStart < start) {
                    val newStyle = if (enabled) spanStyle else (spanStyle and style.inv())
                    if (newStyle != 0) {
                        editable.setSpan(StyleSpan(newStyle), spanStart, start, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                    }
                }
                if (spanEnd > end) {
                    val newStyle = if (enabled) spanStyle else (spanStyle and style.inv())
                    if (newStyle != 0) {
                        editable.setSpan(StyleSpan(newStyle), end, spanEnd, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                    }
                }
            }
        }

        // 如果启用格式，添加新的样式
        if (enabled) {
            editable.setSpan(StyleSpan(style), start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        }

        // 保持光标位置
        setSelection(start, end)
    }

    /**
     * 清除指定范围的所有样式
     */
    private fun clearStyleFromRange(start: Int, end: Int) {
        val editable = text ?: return

        // 移除该范围内所有的样式（StyleSpan）
        val existingStyleSpans = editable.getSpans(start, end, StyleSpan::class.java)
        for (span in existingStyleSpans) {
            val spanStart = editable.getSpanStart(span)
            val spanEnd = editable.getSpanEnd(span)

            editable.removeSpan(span)

            // 保留超出范围的部分
            if (spanStart < start && span.style != 0) {
                editable.setSpan(StyleSpan(span.style), spanStart, start, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            }
            if (spanEnd > end && span.style != 0) {
                editable.setSpan(StyleSpan(span.style), end, spanEnd, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            }
        }

        // 移除该范围内所有的颜色样式（ForegroundColorSpan）
        val existingColorSpans = editable.getSpans(start, end, ForegroundColorSpan::class.java)
        for (span in existingColorSpans) {
            val spanStart = editable.getSpanStart(span)
            val spanEnd = editable.getSpanEnd(span)

            editable.removeSpan(span)

            // 保留超出范围的部分
            if (spanStart < start) {
                editable.setSpan(ForegroundColorSpan(span.foregroundColor), spanStart, start, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            }
            if (spanEnd > end) {
                editable.setSpan(ForegroundColorSpan(span.foregroundColor), end, spanEnd, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            }
        }

        // 移除该范围内所有的字号样式（AbsoluteSizeSpan）
        val existingFontSizeSpans = editable.getSpans(start, end, android.text.style.AbsoluteSizeSpan::class.java)
        for (span in existingFontSizeSpans) {
            editable.removeSpan(span)
        }

        // 移除该范围内所有的描边样式（StrokeSpan）
        val existingStrokeSpans = editable.getSpans(start, end, StrokeSpan::class.java)
        for (span in existingStrokeSpans) {
            val spanStart = editable.getSpanStart(span)
            val spanEnd = editable.getSpanEnd(span)

            editable.removeSpan(span)

            // 保留超出范围的部分
            if (spanStart < start) {
                editable.setSpan(StrokeSpan(span.getStrokeWidth(), span.getStrokeColor()), spanStart, start, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            }
            if (spanEnd > end) {
                editable.setSpan(StrokeSpan(span.getStrokeWidth(), span.getStrokeColor()), end, spanEnd, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            }
        }

        // 移除该范围内所有的字间距样式（LetterSpacingSpan）
        val existingLetterSpacingSpans = editable.getSpans(start, end, LetterSpacingSpan::class.java)
        for (span in existingLetterSpacingSpans) {
            val spanStart = editable.getSpanStart(span)
            val spanEnd = editable.getSpanEnd(span)

            editable.removeSpan(span)

            // 保留超出范围的部分
            if (spanStart < start) {
                editable.setSpan(LetterSpacingSpan(span.getLetterSpacing()), spanStart, start, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            }
            if (spanEnd > end) {
                editable.setSpan(LetterSpacingSpan(span.getLetterSpacing()), end, spanEnd, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            }
        }

        // 保持光标位置
        setSelection(start, end)

        // 🎯 关键修复：强制刷新TextView显示，确保格式清除立即生效
        post {
            invalidate()
            requestLayout()
        }

        AppLog.d("【文本窗口】已清除范围 $start-$end 的所有格式")
    }

    /**
     * 对指定范围应用颜色
     */
    private fun applyColorToRange(start: Int, end: Int, color: Int) {
        val editable = text ?: return

        // 移除该范围内现有的颜色样式
        val existingSpans = editable.getSpans(start, end, ForegroundColorSpan::class.java)
        for (span in existingSpans) {
            val spanStart = editable.getSpanStart(span)
            val spanEnd = editable.getSpanEnd(span)

            editable.removeSpan(span)

            // 保留超出范围的部分
            if (spanStart < start) {
                editable.setSpan(ForegroundColorSpan(span.foregroundColor), spanStart, start, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            }
            if (spanEnd > end) {
                editable.setSpan(ForegroundColorSpan(span.foregroundColor), end, spanEnd, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            }
        }

        // 应用新的颜色样式
        val colorSpan = ForegroundColorSpan(color)
        editable.setSpan(colorSpan, start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)

        // 🎯 关键修复：强制刷新TextView显示，确保颜色立即生效
        post {
            invalidate()
            requestLayout()
        }

        AppLog.d("【文本窗口】已对范围 $start-$end 应用颜色: ${String.format("#%08X", color)}")
    }

    /**
     * 对指定范围应用描边
     */
    private fun applyStrokeToRange(start: Int, end: Int, strokeWidth: Float, strokeColor: Int) {
        val editable = text ?: return

        // 移除该范围内现有的描边样式
        removeStrokeFromRange(start, end)

        // 应用新的描边样式
        val strokeSpan = StrokeSpan(strokeWidth, strokeColor)
        editable.setSpan(strokeSpan, start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)

        AppLog.d("【文本窗口】已对范围 $start-$end 应用描边: 宽度=${strokeWidth}px, 颜色=${String.format("#%08X", strokeColor)}")
    }

    /**
     * 移除指定范围的描边
     */
    private fun removeStrokeFromRange(start: Int, end: Int) {
        val editable = text ?: return

        // 移除该范围内现有的描边样式
        val existingSpans = editable.getSpans(start, end, StrokeSpan::class.java)
        for (span in existingSpans) {
            val spanStart = editable.getSpanStart(span)
            val spanEnd = editable.getSpanEnd(span)

            editable.removeSpan(span)

            // 保留超出范围的部分
            if (spanStart < start) {
                editable.setSpan(StrokeSpan(span.getStrokeWidth(), span.getStrokeColor()), spanStart, start, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            }
            if (spanEnd > end) {
                editable.setSpan(StrokeSpan(span.getStrokeWidth(), span.getStrokeColor()), end, spanEnd, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            }
        }

        AppLog.d("【文本窗口】已移除范围 $start-$end 的描边")
    }

    /**
     * 检测选中文字的格式状态
     */
    fun getSelectionFormatState(): Pair<Boolean, Boolean> {
        if (!isInEditMode) {
            return Pair(isBoldEnabled, isItalicEnabled)
        }

        val start = selectionStart
        val end = selectionEnd

        if (start == end) {
            // 没有选中文字，返回全局格式状态
            return Pair(isBoldEnabled, isItalicEnabled)
        }

        val editable = text ?: return Pair(false, false)
        val spans = editable.getSpans(start, end, StyleSpan::class.java)

        var hasBold = false
        var hasItalic = false

        for (span in spans) {
            val spanStart = editable.getSpanStart(span)
            val spanEnd = editable.getSpanEnd(span)

            // 检查样式是否覆盖了选中范围
            if (spanStart <= start && spanEnd >= end) {
                if ((span.style and Typeface.BOLD) != 0) hasBold = true
                if ((span.style and Typeface.ITALIC) != 0) hasItalic = true
            }
        }

        return Pair(hasBold, hasItalic)
    }

    /**
     * 检测选中文字的颜色
     */
    fun getSelectionColor(): Int? {
        if (!isInEditMode) {
            return null
        }

        val start = selectionStart
        val end = selectionEnd

        if (start == end) {
            // 没有选中文字，返回null
            return null
        }

        val editable = text ?: return null
        val colorSpans = editable.getSpans(start, end, ForegroundColorSpan::class.java)

        // 查找覆盖选中范围的颜色样式
        for (span in colorSpans) {
            val spanStart = editable.getSpanStart(span)
            val spanEnd = editable.getSpanEnd(span)

            // 检查颜色样式是否覆盖了选中范围
            if (spanStart <= start && spanEnd >= end) {
                return span.foregroundColor
            }
        }

        return null // 没有找到颜色样式
    }

    /**
     * 检测选中文字的描边状态
     */
    fun getSelectionStroke(): Triple<Boolean, Float, Int>? {
        if (!isInEditMode) {
            return null
        }

        val start = selectionStart
        val end = selectionEnd

        if (start == end) {
            // 没有选中文字，返回null
            return null
        }

        val editable = text ?: return null
        val strokeSpans = editable.getSpans(start, end, StrokeSpan::class.java)

        // 查找覆盖选中范围的描边样式
        for (span in strokeSpans) {
            val spanStart = editable.getSpanStart(span)
            val spanEnd = editable.getSpanEnd(span)

            // 检查描边样式是否覆盖了选中范围
            if (spanStart <= start && spanEnd >= end) {
                return Triple(true, span.getStrokeWidth(), span.getStrokeColor())
            }
        }

        return Triple(false, 4.0f, 0xFF000000.toInt()) // 没有找到描边样式，返回默认值
    }

    /**
     * 检测选中文字的字体
     */
    fun getSelectionFont(): com.example.castapp.utils.FontPresetManager.FontItem? {
        if (!isInEditMode) {
            return currentFontFamily
        }

        val start = selectionStart
        val end = selectionEnd

        if (start == end) {
            // 没有选中文字，返回当前基础字体
            return currentFontFamily
        }

        val editable = text ?: return currentFontFamily

        // 检查是否有CustomTypefaceSpan
        val customTypefaceSpans = editable.getSpans(start, end, CustomTypefaceSpan::class.java)

        // 查找覆盖选中范围的字体样式
        for (span in customTypefaceSpans) {
            val spanStart = editable.getSpanStart(span)
            val spanEnd = editable.getSpanEnd(span)

            // 检查字体样式是否覆盖了选中范围
            if (spanStart <= start && spanEnd >= end) {
                // 🎯 直接从CustomTypefaceSpan获取FontItem，避免复杂的反向查找
                val fontItem = span.getFontItem()
                if (fontItem != null) {
                    AppLog.d("【文本窗口】检测到选中文字字体: ${fontItem.name}")
                    return fontItem
                } else {
                    // 如果没有保存FontItem，使用反向查找作为后备方案
                    AppLog.w("【文本窗口】CustomTypefaceSpan中没有FontItem，使用反向查找")
                    return findFontItemByTypeface(span.getTypeface())
                }
            }
        }

        // 没有找到字体Span，返回当前基础字体
        return currentFontFamily
    }

    /**
     * 检测选中文字的字间距
     */
    fun getSelectionLetterSpacing(): Float {
        if (!isInEditMode) {
            return 0.0f // 非编辑模式下返回默认字间距
        }

        val start = selectionStart
        val end = selectionEnd

        if (start == end) {
            // 没有选中文字，返回默认字间距
            return 0.0f
        }

        val editable = text ?: return 0.0f

        // 检查是否有LetterSpacingSpan
        val letterSpacingSpans = editable.getSpans(start, end, LetterSpacingSpan::class.java)

        // 查找覆盖选中范围的字间距样式
        for (span in letterSpacingSpans) {
            val spanStart = editable.getSpanStart(span)
            val spanEnd = editable.getSpanEnd(span)

            // 检查字间距样式是否覆盖了选中范围
            if (spanStart <= start && spanEnd >= end) {
                val letterSpacing = span.getLetterSpacing()
                AppLog.d("【文本窗口】检测到选中文字字间距: ${letterSpacing}em")
                return letterSpacing
            }
        }

        // 没有找到字间距Span，返回默认字间距
        return 0.0f
    }

    /**
     * 根据Typeface查找对应的FontItem
     */
    private fun findFontItemByTypeface(targetTypeface: android.graphics.Typeface): com.example.castapp.utils.FontPresetManager.FontItem? {
        try {
            com.example.castapp.utils.FontPresetManager.initialize(context)
            val allFonts = com.example.castapp.utils.FontPresetManager.getAllFonts()

            // 🎯 改进的匹配策略：使用字体属性比较而不是对象引用比较
            for (fontItem in allFonts) {
                val fontTypeface = fontItem.loadTypeface()
                if (fontTypeface != null) {
                    // 比较字体的基本属性
                    if (isSameTypeface(targetTypeface, fontTypeface)) {
                        AppLog.d("【文本窗口】找到匹配的字体: ${fontItem.name}")
                        return fontItem
                    }
                }
            }

            // 🎯 特殊处理：如果是系统默认字体，返回Roboto
            if (targetTypeface == android.graphics.Typeface.DEFAULT ||
                targetTypeface == android.graphics.Typeface.SANS_SERIF) {
                val robotoFont = allFonts.find { it.name == "Roboto" }
                if (robotoFont != null) {
                    AppLog.d("【文本窗口】检测到系统默认字体，返回Roboto")
                    return robotoFont
                }
            }

            // 如果没找到匹配的，返回当前基础字体
            AppLog.w("【文本窗口】未找到匹配的字体，返回当前基础字体")
            return currentFontFamily

        } catch (e: Exception) {
            AppLog.e("【文本窗口】查找字体失败", e)
            return currentFontFamily
        }
    }

    /**
     * 比较两个Typeface是否相同
     */
    private fun isSameTypeface(typeface1: android.graphics.Typeface, typeface2: android.graphics.Typeface): Boolean {
        // 首先检查引用是否相同
        if (typeface1 === typeface2) {
            return true
        }

        // 比较字体样式
        if (typeface1.style != typeface2.style) {
            return false
        }

        // 比较字体的字符串表示（包含字体文件信息）
        val typeface1String = typeface1.toString()
        val typeface2String = typeface2.toString()

        AppLog.v("【文本窗口】字体比较: typeface1=$typeface1String, typeface2=$typeface2String")

        return typeface1String == typeface2String
    }

    /**
     * 检测选中文字的格式状态（包含字号）
     */
    fun getSelectionFormatStateWithFontSize(): Triple<Boolean, Boolean, Int> {
        if (!isInEditMode) {
            return Triple(isBoldEnabled, isItalicEnabled, getCurrentFontSize())
        }

        val start = selectionStart
        val end = selectionEnd

        if (start == end) {
            // 没有选中文字，返回全局格式状态
            val globalFontSize = getCurrentFontSize()
            AppLog.v("【文本窗口】无选中文字，返回全局字号: ${globalFontSize}sp")
            return Triple(isBoldEnabled, isItalicEnabled, globalFontSize)
        }

        val editable = text ?: return Triple(false, false, getCurrentFontSize())
        val styleSpans = editable.getSpans(start, end, StyleSpan::class.java)
        val fontSizeSpans = editable.getSpans(start, end, android.text.style.AbsoluteSizeSpan::class.java)

        var hasBold = false
        var hasItalic = false
        var fontSize = getCurrentFontSize()

        // 检查样式
        for (span in styleSpans) {
            val spanStart = editable.getSpanStart(span)
            val spanEnd = editable.getSpanEnd(span)

            if (spanStart <= start && spanEnd >= end) {
                if ((span.style and Typeface.BOLD) != 0) hasBold = true
                if ((span.style and Typeface.ITALIC) != 0) hasItalic = true
            }
        }

        // 检查字号 - 使用自定义标记来避免像素转换问题
        for (span in fontSizeSpans) {
            val spanStart = editable.getSpanStart(span)
            val spanEnd = editable.getSpanEnd(span)

            if (spanStart <= start && spanEnd >= end) {
                // 尝试从Span的标记中获取原始sp值
                fontSize = extractSpValueFromSpan(span)
                AppLog.v("【文本窗口】检测到字号Span: ${fontSize}sp")
                break
            }
        }

        return Triple(hasBold, hasItalic, fontSize)
    }

    /**
     * 对指定范围应用字号
     */
    private fun applyFontSizeToRange(start: Int, end: Int, fontSize: Int) {
        val editable = text ?: return

        // 移除该范围内现有的字号样式
        val existingSpans = editable.getSpans(start, end, android.text.style.AbsoluteSizeSpan::class.java)
        for (span in existingSpans) {
            editable.removeSpan(span)
        }

        // 创建带有sp值标记的字号样式
        val fontSizeSpan = createFontSizeSpanWithSpValue(fontSize)
        editable.setSpan(fontSizeSpan, start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)

        AppLog.d("【文本窗口】已对范围应用字号: $start-$end, 字号: ${fontSize}sp")
    }

    /**
     * 对指定范围应用字间距
     * 🎯 修复：改进字间距应用逻辑，避免影响其他格式
     */
    private fun applyLetterSpacingToRange(start: Int, end: Int, letterSpacing: Float) {
        val editable = text ?: return

        try {
            // 🎯 关键修复：更精确地处理重叠的字间距Span
            val existingSpans = editable.getSpans(start, end, LetterSpacingSpan::class.java)

            // 记录需要保留的其他格式Span
            val otherSpans = mutableListOf<Pair<Any, Pair<Int, Int>>>()
            val allSpans = editable.getSpans(start, end, Any::class.java)

            for (span in allSpans) {
                if (span !is LetterSpacingSpan) {
                    val spanStart = editable.getSpanStart(span)
                    val spanEnd = editable.getSpanEnd(span)
                    otherSpans.add(span to (spanStart to spanEnd))
                }
            }

            AppLog.d("【文本窗口】字间距应用前检查: 范围$start-$end, 现有字间距Span=${existingSpans.size}个, 其他Span=${otherSpans.size}个")

            // 移除现有的字间距样式（只移除完全重叠的）
            for (span in existingSpans) {
                val spanStart = editable.getSpanStart(span)
                val spanEnd = editable.getSpanEnd(span)

                // 只移除完全重叠或包含在目标范围内的字间距Span
                if (spanStart >= start && spanEnd <= end) {
                    editable.removeSpan(span)
                    AppLog.d("【文本窗口】移除重叠的字间距Span: $spanStart-$spanEnd")
                }
            }

            // 如果字间距不为0，应用新的字间距样式
            if (letterSpacing != 0.0f) {
                val letterSpacingSpan = LetterSpacingSpan(letterSpacing)
                editable.setSpan(letterSpacingSpan, start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                AppLog.d("【文本窗口】应用新字间距Span: $start-$end, 字间距=${letterSpacing}em")
            }

            // 验证其他格式是否仍然存在
            val remainingSpans = editable.getSpans(start, end, Any::class.java)
            val remainingOtherSpans = remainingSpans.filter { it !is LetterSpacingSpan }
            AppLog.d("【文本窗口】字间距应用后验证: 剩余其他Span=${remainingOtherSpans.size}个")

            AppLog.d("【文本窗口】字间距应用完成: $start-$end, 字间距: ${letterSpacing}em")

        } catch (e: Exception) {
            AppLog.e("【文本窗口】应用字间距失败: $start-$end", e)
        }
    }

    /**
     * 对指定范围应用字体
     */
    private fun applyFontFamilyToRange(start: Int, end: Int, fontItem: com.example.castapp.utils.FontPresetManager.FontItem?) {
        val editable = text ?: return

        try {
            // 移除该范围内现有的字体样式
            val existingTypefaceSpans = editable.getSpans(start, end, android.text.style.TypefaceSpan::class.java)
            for (span in existingTypefaceSpans) {
                editable.removeSpan(span)
            }

            // 移除该范围内现有的CustomTypefaceSpan
            val existingCustomSpans = editable.getSpans(start, end, CustomTypefaceSpan::class.java)
            for (span in existingCustomSpans) {
                editable.removeSpan(span)
            }

            // 如果字体不为空，应用新字体
            if (fontItem != null) {
                val typeface = fontItem.loadTypeface()
                if (typeface != null) {
                    // 🎯 特殊处理：如果是Roboto字体且选中全部文字，直接设置基础字体以保持一致性
                    if (fontItem.name == "Roboto" && start == 0 && end == editable.length) {
                        // 移除所有字体相关的Span
                        removeAllFontSpansFromText()
                        // 设置基础字体
                        setFontFamily(fontItem)
                        AppLog.d("【文本窗口】全文应用Roboto字体，使用基础字体设置: ${fontItem.name}")
                    } else {
                        // 创建自定义TypefaceSpan，同时保存FontItem引用
                        val typefaceSpan = CustomTypefaceSpan(typeface, fontItem)
                        editable.setSpan(typefaceSpan, start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                        AppLog.d("【文本窗口】已对范围应用字体: $start-$end, 字体: ${fontItem.name}")
                    }
                } else {
                    AppLog.w("【文本窗口】字体加载失败: ${fontItem.name}")
                }
            } else {
                // 字体为null时，确保移除所有字体样式，恢复默认字体
                AppLog.d("【文本窗口】移除范围 $start-$end 的字体样式，恢复默认字体")
            }

        } catch (e: Exception) {
            AppLog.e("【文本窗口】应用字体到范围失败", e)
        }
    }

    /**
     * 移除文本中所有的字体相关Span
     */
    private fun removeAllFontSpansFromText() {
        val editable = text ?: return

        try {
            var totalRemoved = 0

            // 移除所有TypefaceSpan
            val typefaceSpans = editable.getSpans(0, editable.length, android.text.style.TypefaceSpan::class.java)
            for (span in typefaceSpans) {
                editable.removeSpan(span)
                totalRemoved++
            }

            // 移除所有CustomTypefaceSpan
            val customTypefaceSpans = editable.getSpans(0, editable.length, CustomTypefaceSpan::class.java)
            for (span in customTypefaceSpans) {
                editable.removeSpan(span)
                totalRemoved++
            }

            AppLog.d("【文本窗口】已移除所有字体Span，TypefaceSpan: ${typefaceSpans.size}个, CustomTypefaceSpan: ${customTypefaceSpans.size}个, 总计: ${totalRemoved}个")

        } catch (e: Exception) {
            AppLog.e("【文本窗口】移除字体Span失败", e)
        }
    }

    /**
     * 创建带有sp值标记的字号Span
     */
    private fun createFontSizeSpanWithSpValue(fontSizeSp: Int): android.text.style.AbsoluteSizeSpan {
        // 使用一个Map来存储Span到sp值的映射
        val span = android.text.style.AbsoluteSizeSpan(fontSizeSp, true)
        spanToSpValueMap[span] = fontSizeSp
        return span
    }

    /**
     * 从Span中提取原始sp值
     */
    private fun extractSpValueFromSpan(span: android.text.style.AbsoluteSizeSpan): Int {
        // 首先尝试从映射中获取
        spanToSpValueMap[span]?.let { return it }

        // 🎯 关键修复：如果span.dip为true，说明size就是sp值
        if (span.dip) {
            AppLog.d("【文本窗口】从dip=true的Span直接获取sp值: ${span.size}sp")
            return span.size
        }

        // 如果映射中没有且dip=false，使用像素转换作为后备方案
        return pixelToSp(span.size)
    }

    // Span到sp值的映射表
    private val spanToSpValueMap = mutableMapOf<android.text.style.AbsoluteSizeSpan, Int>()

    /**
     * 将像素值转换为sp值（后备方案）
     */
    private fun pixelToSp(pixels: Int): Int {
        val density = context.resources.displayMetrics.density
        val spValue = pixels / density
        val roundedSp = (spValue + 0.5f).toInt() // +0.5f用于四舍五入

        AppLog.w("【文本窗口】使用像素转sp后备方案: ${pixels}px → ${roundedSp}sp")

        return roundedSp
    }
    
    override fun onSelectionChanged(selStart: Int, selEnd: Int) {
        super.onSelectionChanged(selStart, selEnd)

        // 在编辑模式下，当选择发生变化时，通知监听器更新按钮状态
        if (isInEditMode) {
            val formatState = getSelectionFormatState()
            val textColor = getSelectionColor()
            val strokeState = getSelectionStroke()

            // 调用原有的监听器（向后兼容）
            onSelectionChangeListener?.invoke(formatState.first, formatState.second)

            // 调用新的包含颜色信息的监听器
            onSelectionChangeWithColorListener?.invoke(formatState.first, formatState.second, textColor)

            // 调用新的包含所有格式信息的监听器
            onSelectionChangeWithAllFormatsListener?.invoke(formatState.first, formatState.second, textColor, strokeState)

            AppLog.v("【文本窗口】选择变化: $selStart-$selEnd, 格式状态: 加粗=${formatState.first}, 倾斜=${formatState.second}, 颜色=${textColor?.let { String.format("#%08X", it) } ?: "默认"}, 描边=${strokeState?.let { "(${it.first}, ${it.second}px, ${String.format("#%08X", it.third)})" } ?: "无"}")
        }
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        // 编辑模式下，让EditText正常处理触摸事件
        if (isInEditMode) {
            AppLog.v("【文本窗口】编辑模式 - 使用EditText默认触摸处理")
            return super.onTouchEvent(event)
        }

        // 非编辑模式下，处理双击和拖动
        val gestureHandled = gestureDetector.onTouchEvent(event)

        AppLog.v("【文本窗口】触摸事件: ${event.action}, 手势处理结果: $gestureHandled")

        // 🎯 重要：只有在真正处理了双击事件时才返回true
        // 对于其他情况，都返回false让父容器处理拖动
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                AppLog.v("【文本窗口】ACTION_DOWN - 不消费，让父容器处理拖动")
                // 关键修复：不消费DOWN事件，让父容器能够接收整个事件序列
                return false
            }
            MotionEvent.ACTION_UP -> {
                AppLog.v("【文本窗口】ACTION_UP - 事件序列结束")
                performClick() // 确保可访问性
                return false
            }
            MotionEvent.ACTION_MOVE -> {
                AppLog.v("【文本窗口】ACTION_MOVE - 不消费，让父容器处理拖动")
                return false
            }
            else -> {
                return false
            }
        }
    }

    override fun performClick(): Boolean {
        super.performClick()
        return true
    }

    /**
     * 设置窗口背景颜色
     */
    fun setWindowBackgroundColor(enabled: Boolean, color: Int) {
        try {
            isWindowColorEnabled = enabled
            windowBackgroundColor = color

            if (enabled) {
                // 启用窗口背景颜色
                setBackgroundColor(color)
                AppLog.d("【文本窗口】窗口背景颜色已设置: ${String.format("#%08X", color)}")
            } else {
                // 禁用窗口背景颜色，恢复透明背景
                background = null
                AppLog.d("【文本窗口】窗口背景颜色已禁用，恢复透明背景")
            }
        } catch (e: Exception) {
            AppLog.e("【文本窗口】设置窗口背景颜色失败", e)
        }
    }

    /**
     * 获取当前窗口背景颜色状态
     */
    fun getWindowBackgroundColorState(): Pair<Boolean, Int> {
        return Pair(isWindowColorEnabled, windowBackgroundColor)
    }
}

/**
 * 自定义TypefaceSpan，支持自定义Typeface
 */
class CustomTypefaceSpan(
    private val typeface: android.graphics.Typeface,
    private val fontItem: com.example.castapp.utils.FontPresetManager.FontItem? = null
) : android.text.style.MetricAffectingSpan() {

    override fun updateDrawState(ds: android.text.TextPaint) {
        applyCustomTypeface(ds, typeface)
    }

    override fun updateMeasureState(paint: android.text.TextPaint) {
        applyCustomTypeface(paint, typeface)
    }

    private fun applyCustomTypeface(paint: android.text.TextPaint, tf: android.graphics.Typeface) {
        val oldStyle: Int
        val old = paint.typeface
        oldStyle = old?.style ?: 0

        val fake = oldStyle and tf.style.inv()
        if (fake and android.graphics.Typeface.BOLD != 0) {
            paint.isFakeBoldText = true
        }

        if (fake and android.graphics.Typeface.ITALIC != 0) {
            paint.textSkewX = -0.25f
        }

        paint.typeface = tf
    }

    /**
     * 获取字体对象
     */
    fun getTypeface(): android.graphics.Typeface {
        return typeface
    }

    /**
     * 获取字体项
     */
    fun getFontItem(): com.example.castapp.utils.FontPresetManager.FontItem? {
        return fontItem
    }
}
