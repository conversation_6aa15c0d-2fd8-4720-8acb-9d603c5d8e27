<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;ba33adda-9ea5-43bc-b8fd-8e04eae963c0&quot;,&quot;conversations&quot;:{&quot;ba33adda-9ea5-43bc-b8fd-8e04eae963c0&quot;:{&quot;id&quot;:&quot;ba33adda-9ea5-43bc-b8fd-8e04eae963c0&quot;,&quot;createdAtIso&quot;:&quot;2025-08-19T05:07:48.570Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-08-19T05:07:57.821Z&quot;,&quot;chatHistory&quot;:[{&quot;request_id&quot;:&quot;ac4fa083-b326-4859-9e16-6b543ab3041c&quot;,&quot;uuid&quot;:&quot;cccd57fe-74dc-4494-bc8f-8259eac3b2cf&quot;,&quot;chatItemType&quot;:&quot;agentic-checkpoint-delimiter&quot;,&quot;status&quot;:&quot;success&quot;,&quot;fromTimestamp&quot;:0,&quot;toTimestamp&quot;:1755580068575,&quot;seen_state&quot;:&quot;seen&quot;},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;dc97f08d-0328-456e-9f34-09ad7ea0f872&quot;,&quot;request_message&quot;:&quot;你好&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;你好&quot;}]}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:1,&quot;type&quot;:2,&quot;content&quot;:&quot;**⚠️ Your subscription <NAME_EMAIL> is inactive. Please update your plan [here](https://app.augmentcode.com/account?utm_source=vscode&amp;utm_medium=in_agent_conversation&amp;utm_campaign=inactive_subscription_reminder&amp;utm_content=here) to continue using Augment. ⚠️**&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;你好&quot;}},{&quot;id&quot;:1,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/Android/AndroidProject/CastAPP&quot;,&quot;folderRoot&quot;:&quot;D:/Android/AndroidProject/CastAPP&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-08-19T05:07:57.832Z&quot;,&quot;response_text&quot;:&quot;**⚠️ Your subscription <NAME_EMAIL> is inactive. Please update your plan [here](https://app.augmentcode.com/account?utm_source=vscode&amp;utm_medium=in_agent_conversation&amp;utm_campaign=inactive_subscription_reminder&amp;utm_content=here) to continue using Augment. ⚠️**&quot;,&quot;workspace_file_chunks&quot;:[]}],&quot;feedbackStates&quot;:{&quot;temp-fe-b42a1642-3c02-4d82-9978-18b5b2bc5875&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:true,&quot;extraData&quot;:{&quot;isAgentConversation&quot;:true,&quot;hasDirtyEdits&quot;:false,&quot;baselineTimestamp&quot;:0},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;a0aaead4-2e1b-4d2f-9449-3de834d93cf8&quot;}},&quot;agentExecutionMode&quot;:&quot;auto&quot;,&quot;isPanelCollapsed&quot;:false,&quot;displayedAnnouncements&quot;:[],&quot;sortConversationsBy&quot;:&quot;lastMessageTimestamp&quot;,&quot;sendMode&quot;:&quot;send&quot;}" />
      </map>
    </option>
  </component>
</project>